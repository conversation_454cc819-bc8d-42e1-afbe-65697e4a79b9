
import type {NextConfig} from 'next';

const nextConfig: NextConfig = {
  /* config options here */
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'placehold.co',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'cdn.jsdelivr.net',
        port: '',
        pathname: '/gh/atomiclabs/cryptocurrency-icons/**',
      },
    ],
    // Allow serving local images from /public (though this is usually default for next/image)
    // Adding an explicit domain for localhost might be needed if running with a custom hostname during dev
    // For images served directly from /public/uploads, next/image usually doesn't need specific config
    // as long as the src path is relative to /public (e.g., /uploads/image.png)
  },
  // Note: The api.bodyParser configuration is not supported in App Router.
  // For file upload size limits in App Router, you need to handle this in individual route handlers
  // using the Request object and checking content-length or handling multipart data appropriately.
};

export default nextConfig;

    