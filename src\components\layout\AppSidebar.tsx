
"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Sidebar,
  SidebarHeader,
  SidebarContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarFooter,
} from "@/components/ui/sidebar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { 
  LayoutDashboard, 
  Briefcase, 
  // BellRing, // Icon removed as page is removed
  Newspaper, 
  GraduationCap, 
  Settings,
  Rocket,
  LogOut
} from "lucide-react";


const navItems = [
  { href: "/", label: "Dashboard", icon: LayoutDashboard },
  { href: "/portfolio", label: "Portfolio", icon: Briefcase },
  // { href: "/alerts", label: "Price Alerts", icon: BellRing }, // Page removed
  { href: "/news", label: "News Summary", icon: Newspaper },
  { href: "/onboarding", label: "Onboarding", icon: GraduationCap },
];

const bottomNavItems = [
  { href: "/settings", label: "Settings", icon: Settings },
];

export function AppSidebar() {
  const pathname = usePathname();

  return (
    <Sidebar 
      collapsible="icon" 
      variant="sidebar" 
      side="left" 
      className="border-r"
      sheetTitle="CryptoPilot Menu"
    >
      <SidebarHeader className="p-4 flex items-center gap-2 justify-center group-data-[collapsible=icon]:justify-center">
        <Link href="/" passHref legacyBehavior>
          <a className="flex items-center gap-2 text-primary hover:text-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-background rounded-md transition-colors">
            <span 
              aria-hidden="true" 
              className="flex items-center justify-center h-10 w-10 group-data-[collapsible=icon]:h-8 group-data-[collapsible=icon]:w-8"
            >
              <Rocket className="h-7 w-7 group-data-[collapsible=icon]:h-6 group-data-[collapsible=icon]:w-6" />
            </span>
            <h1 className="text-2xl font-semibold text-inherit group-data-[collapsible=icon]:hidden">
              CryptoPilot
            </h1>
          </a>
        </Link>
      </SidebarHeader>
      <SidebarContent>
        <ScrollArea className="h-full">
          <SidebarMenu className="px-2 flex-grow">
            {navItems.map((item) => (
              <SidebarMenuItem key={item.href}>
                <Link href={item.href} passHref legacyBehavior>
                  <SidebarMenuButton
                    asChild
                    isActive={pathname === item.href}
                    className={cn(
                      "w-full justify-start",
                      pathname === item.href && "bg-primary/10 text-primary hover:bg-primary/20"
                    )}
                    tooltip={item.label}
                  >
                    <a>
                      <item.icon className="h-5 w-5" />
                      <span className="group-data-[collapsible=icon]:hidden">{item.label}</span>
                    </a>
                  </SidebarMenuButton>
                </Link>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </ScrollArea>
      </SidebarContent>
      <SidebarFooter className="p-2 mt-auto">
        <SidebarMenu className="px-0">
          {bottomNavItems.map((item) => (
            <SidebarMenuItem key={item.href}>
               <Link href={item.href} passHref legacyBehavior>
                <SidebarMenuButton
                  asChild
                  isActive={pathname === item.href}
                  className={cn(
                    "w-full justify-start",
                    pathname === item.href && "bg-primary/10 text-primary hover:bg-primary/20"
                  )}
                  tooltip={item.label}
                >
                  <a>
                    <item.icon className="h-5 w-5" />
                    <span className="group-data-[collapsible=icon]:hidden">{item.label}</span>
                  </a>
                </SidebarMenuButton>
              </Link>
            </SidebarMenuItem>
          ))}
           <SidebarMenuItem>
              <SidebarMenuButton
                className="w-full justify-start"
                tooltip="Logout"
              >
                <LogOut className="h-5 w-5" />
                <span className="group-data-[collapsible=icon]:hidden">Logout</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
