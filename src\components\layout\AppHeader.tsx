
"use client";

import Link from "next/link";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  User, 
  Settings, 
  LogOut, 
  type LucideIcon, // Use type import
  UserCircle,
  Smile,
  Bot,
  Rocket,
  Briefcase,
  ShieldCheck,
  Coffee,
  Gamepad2,
  Code2
} from "lucide-react";
import { useState, useEffect } from "react";

interface ProfileIcon {
  name: string;
  IconComponent: LucideIcon;
}

const availableIcons: ProfileIcon[] = [
  { name: "UserCircle", IconComponent: UserCircle },
  { name: "Smile", IconComponent: Smile },
  { name: "<PERSON><PERSON>", IconComponent: <PERSON><PERSON> },
  { name: "<PERSON>", IconComponent: <PERSON> },
  { name: "Briefcase", IconComponent: Briefcase },
  { name: "ShieldCheck", IconComponent: ShieldCheck },
  { name: "Coffee", IconComponent: Coffee },
  { name: "Gamepad2", IconComponent: Gamepad2 },
  { name: "Code2", IconComponent: Code2 },
];


export function AppHeader() {
  const [avatarImageSrc, setAvatarImageSrc] = useState<string | null>(null);
  const [selectedIconComponent, setSelectedIconComponent] = useState<LucideIcon | null>(null);
  const [avatarFallbackText, setAvatarFallbackText] = useState<string>("CP");
  const [avatarFullName, setAvatarFullName] = useState<string | null>(null);

  useEffect(() => {
    // Function to update display based on localStorage, only runs client-side
    const updateDisplayFromLocalStorage = () => {
      const storedUploadedAvatar = localStorage.getItem("userUploadedAvatar");
      const storedIconName = localStorage.getItem("userSelectedAvatarIcon");
      const currentFullName = localStorage.getItem("userFullName");
      
      setAvatarFullName(currentFullName);

      let fallbackInitials = "CP";
      if (currentFullName) {
        const names = currentFullName.split(' ').filter(Boolean);
        if (names.length > 1) {
          fallbackInitials = (names[0][0] || '') + (names[names.length - 1][0] || '');
        } else if (names.length === 1 && names[0].length > 1) {
          fallbackInitials = names[0].substring(0, 2);
        } else if (names.length === 1 && names[0].length === 1) {
            fallbackInitials = names[0][0];
        }
      }
      fallbackInitials = fallbackInitials.toUpperCase();
      
      let singleInitialForImageFallback = "U";
      if (currentFullName && currentFullName[0]) {
        singleInitialForImageFallback = currentFullName[0].toUpperCase();
      }

      if (storedUploadedAvatar) {
        setAvatarImageSrc(storedUploadedAvatar);
        setSelectedIconComponent(null);
        setAvatarFallbackText(singleInitialForImageFallback); 
      } else if (storedIconName) {
        const iconData = availableIcons.find(icon => icon.name === storedIconName);
        const IconToSet = iconData ? iconData.IconComponent : UserCircle;
        setSelectedIconComponent(IconToSet);
        setAvatarImageSrc(null);
        if (IconToSet !== UserCircle) {
          setAvatarFallbackText(""); // No text if specific icon is chosen
        } else {
          setAvatarFallbackText(fallbackInitials); // Initials for default UserCircle
        }
      } else {
        // Default if nothing set
        setSelectedIconComponent(UserCircle);
        setAvatarImageSrc(null);
        setAvatarFallbackText(fallbackInitials);
      }
    };

    updateDisplayFromLocalStorage(); // Initial load

    const handleProfileUpdateEvent = () => {
      updateDisplayFromLocalStorage(); // Reload on event
    };

    window.addEventListener('profileUpdated', handleProfileUpdateEvent);

    return () => {
      window.removeEventListener('profileUpdated', handleProfileUpdateEvent);
    };
  }, []);


  const DisplayIcon = selectedIconComponent || UserCircle;

  // Determine the final fallback text to pass to AvatarFallback
  let currentFallbackTextForAvatar = "CP"; // Default
  if (avatarImageSrc) {
    // If there's an image, fallback is a single initial if the image fails to load
    currentFallbackTextForAvatar = (avatarFullName && avatarFullName[0]) ? avatarFullName[0].toUpperCase() : "U";
  } else if (selectedIconComponent && selectedIconComponent !== UserCircle) {
    // If a specific icon (not UserCircle) is selected, no text fallback is needed
    currentFallbackTextForAvatar = "";
  } else {
    // If default UserCircle is active or no specific icon, use calculated initials or "CP"
    currentFallbackTextForAvatar = avatarFallbackText;
  }


  return (
    <header className="sticky top-0 z-10 flex h-16 items-center gap-4 border-b bg-background/80 backdrop-blur-sm px-4 md:px-6">
      <SidebarTrigger className="md:hidden" />
      <div className="flex-1">
        {/* Future: Breadcrumbs or page title can go here */}
      </div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="relative h-10 w-10 rounded-full">
            <Avatar className="h-10 w-10 border border-border">
              {avatarImageSrc ? (
                <>
                  <AvatarImage src={avatarImageSrc} alt={avatarFullName || "User Avatar"} data-ai-hint="user photo" />
                  {/* Fallback for when image fails to load */}
                  <AvatarFallback className="text-sm font-semibold bg-muted text-muted-foreground">
                    {(avatarFullName && avatarFullName[0]) ? avatarFullName[0].toUpperCase() : "U"}
                  </AvatarFallback>
                </>
              ) : (
                <>
                  {/* Display selected icon or default UserCircle */}
                  <DisplayIcon className="h-full w-full p-2 text-muted-foreground" />
                  {/* Fallback text only if it's the default UserCircle AND no image is uploaded */}
                  {DisplayIcon === UserCircle && !avatarImageSrc && currentFallbackTextForAvatar && (
                    <AvatarFallback className="text-sm font-semibold bg-muted text-muted-foreground">
                       {currentFallbackTextForAvatar}
                    </AvatarFallback>
                  )}
                </>
              )}
            </Avatar>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel>{avatarFullName || "My Account"}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <Link href="/profile" passHref legacyBehavior>
            <DropdownMenuItem asChild>
              <a>
                <User className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </a>
            </DropdownMenuItem>
          </Link>
          <Link href="/settings" passHref legacyBehavior>
            <DropdownMenuItem asChild>
              <a>
                <Settings className="mr-2 h-4 w-4" />
                <span>Settings</span>
              </a>
            </DropdownMenuItem>
          </Link>
          <DropdownMenuSeparator />
          <DropdownMenuItem>
            <LogOut className="mr-2 h-4 w-4" />
            <span>Log out</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </header>
  );
}
    