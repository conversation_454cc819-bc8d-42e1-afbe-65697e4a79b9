
"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "@/hooks/use-toast";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { BellPlus, Search } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Check } from "lucide-react";
import { cn } from "@/lib/utils";
import React from "react";

interface CryptoInfo {
  symbol: string;
  name: string;
}

const alertFormSchema = z.object({
  cryptoSymbol: z.string().min(1, "Please select a cryptocurrency."),
  condition: z.enum(["above", "below"]),
  targetPrice: z.coerce
    .number({ invalid_type_error: "Target price must be a number." })
    .positive("Target price must be positive.")
    .refine(val => !isNaN(val), { message: "Invalid number format for target price."}),
});

type AlertFormValues = z.infer<typeof alertFormSchema>;

interface CreateAlertFormProps {
  onAlertCreated: (alert: AlertFormValues) => void;
  availableCryptos: CryptoInfo[];
}

export function CreateAlertForm({ onAlertCreated, availableCryptos }: CreateAlertFormProps) {
  const [popoverOpen, setPopoverOpen] = React.useState(false);

  const form = useForm<AlertFormValues>({
    resolver: zodResolver(alertFormSchema),
    defaultValues: {
      cryptoSymbol: "",
      condition: "above",
      targetPrice: undefined, // Set to undefined so placeholder shows and positive validation works correctly for empty initial state
    },
  });

  function onSubmit(data: AlertFormValues) {
    onAlertCreated(data);
    toast({
      title: "Alert Created!",
      description: `You'll be notified when ${data.cryptoSymbol} goes ${data.condition} $${data.targetPrice.toLocaleString()}.`,
    });
    form.reset();
    // Ensure cryptoSymbol is truly reset for the combobox
    // @ts-ignore 
    form.setValue("cryptoSymbol", "", { shouldValidate: false }); 
    form.setValue("targetPrice", undefined, { shouldValidate: false });
  }

  return (
    <Card className="card-interactive">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BellPlus className="h-6 w-6 text-primary" />
          Create New Price Alert
        </CardTitle>
        <CardDescription>Set up notifications for specific price targets.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="cryptoSymbol"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Cryptocurrency</FormLabel>
                  <Popover open={popoverOpen} onOpenChange={setPopoverOpen}>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          role="combobox"
                          className={cn(
                            "w-full justify-between",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value
                            ? availableCryptos.find(
                                (crypto) => crypto.symbol === field.value
                              )?.name || field.value 
                            : "Select a coin"}
                           <Search className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-[--radix-popover-trigger-width] max-h-[--radix-popover-content-available-height] p-0">
                      <Command>
                        <CommandInput placeholder="Search coin..." />
                        <CommandList>
                          <CommandEmpty>No cryptocurrency found.</CommandEmpty>
                          <CommandGroup>
                            {availableCryptos.map((crypto) => (
                              <CommandItem
                                value={crypto.symbol} // Use symbol for value
                                key={crypto.symbol}
                                onSelect={() => { // Use symbol for onSelect
                                  form.setValue("cryptoSymbol", crypto.symbol);
                                  setPopoverOpen(false);
                                }}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    field.value === crypto.symbol ? "opacity-100" : "opacity-0"
                                  )}
                                />
                                {crypto.name} ({crypto.symbol})
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="condition"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Condition</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value} defaultValue="above">
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select condition" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="above">Price is Above</SelectItem>
                        <SelectItem value="below">Price is Below</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="targetPrice"
                render={({ field: { onChange, ...restField } }) => (
                  <FormItem>
                    <FormLabel>Target Price (USD)</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="e.g., 65,000.50"
                        {...restField}
                        value={restField.value === undefined ? "" : String(restField.value)} // Display purposes
                        onChange={(e) => {
                          const rawValue = e.target.value;
                          if (rawValue === "") {
                            onChange(undefined); // Pass undefined for Zod to handle empty state
                            return;
                          }
                          // Remove non-numeric characters except for the decimal point.
                          const cleanedValue = rawValue.replace(/[^0-9.]/g, "");
                          // Ensure only one decimal point
                          const parts = cleanedValue.split('.');
                          let finalValueToParse = cleanedValue;
                          if (parts.length > 2) {
                            finalValueToParse = parts[0] + '.' + parts.slice(1).join('');
                          }
                          // Pass the string that might become a number to RHF
                          // Zod will attempt to coerce it.
                          onChange(finalValueToParse);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <Button type="submit" className="w-full md:w-auto bg-accent hover:bg-accent/90 text-accent-foreground">
              Create Alert
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
