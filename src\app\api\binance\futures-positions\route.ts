
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import Binance from 'node-binance-api';
import type { OpenPosition } from '@/lib/types';

export async function POST(request: NextRequest) {
  try {
    let body;
    try {
      body = await request.json();
    } catch (parseError) {
      console.error("Erro ao fazer parse do JSON da requisição:", parseError);
      return NextResponse.json({ success: false, message: "Corpo da requisição inválido ou ausente." }, { status: 400 });
    }
    
    const { apiKey, apiSecret } = body;

    if (!apiKey || !apiSecret) {
      return NextResponse.json({ success: false, message: "API Key ou Secret ausentes." }, { status: 400 });
    }

    const binance = new Binance().options({
      APIKEY: apiKey,
      APISECRET: apiSecret,
      family: 4,
    });

    let positionsData;
    try {
      positionsData = await binance.futuresPositionRisk(); 
    } catch (e: any) {
      console.error("Erro ao buscar posições de futuros da Binance:", e.body || e.message || e);
      const errorMsg = e.body ? JSON.parse(e.body).msg : e.message;
      return NextResponse.json({ success: false, message: `Erro da API Binance (Futuros): ${errorMsg || 'Não foi possível obter posições.'}` }, { status: 500 });
    }

    if (!positionsData || !Array.isArray(positionsData)) {
        return NextResponse.json({ success: true, data: { positions: [], totalUnrealizedFuturesPnlUSDT: 0 } });
    }
    
    let totalUnrealizedFuturesPnlUSDT = 0;

    const openPositions: OpenPosition[] = positionsData
      .filter((pos: any) => parseFloat(pos.positionAmt) !== 0)
      .map((pos: any) => {
        const quantity = parseFloat(pos.positionAmt);
        const entryPrice = parseFloat(pos.entryPrice);
        const markPrice = parseFloat(pos.markPrice);
        const unrealizedProfit = parseFloat(pos.unRealizedProfit);
        const initialMargin = parseFloat(pos.initialMargin) || (entryPrice * Math.abs(quantity) / (parseFloat(pos.leverage) || 1)); 

        totalUnrealizedFuturesPnlUSDT += unrealizedProfit;

        let pnlPercentage = 0;
        if (initialMargin !== 0) {
          pnlPercentage = (unrealizedProfit / initialMargin) * 100;
        } else if (entryPrice !== 0 && quantity !== 0) {
          pnlPercentage = ((markPrice - entryPrice) / entryPrice) * (quantity > 0 ? 1 : -1) * (parseFloat(pos.leverage) || 1) * 100 ;
        }


        return {
          id: pos.symbol + (pos.positionSide || (quantity > 0 ? 'LONG' : 'SHORT')), 
          cryptoSymbol: pos.symbol.replace('USDT', '').replace('BUSD',''), 
          cryptoName: pos.symbol.replace('USDT', '').replace('BUSD',''), 
          type: quantity > 0 ? 'long' : 'short',
          entryPrice: entryPrice,
          currentPrice: markPrice,
          quantity: Math.abs(quantity),
          liquidationPrice: parseFloat(pos.liquidationPrice),
          margin: parseFloat(pos.isolatedWallet) || initialMargin, 
          pnl: unrealizedProfit,
          pnlPercentage: parseFloat(pnlPercentage.toFixed(2)),
          exchange: 'Binance Futures',
        };
      });

    return NextResponse.json({ 
        success: true, 
        data: { 
            positions: openPositions,
            totalUnrealizedFuturesPnlUSDT: parseFloat(totalUnrealizedFuturesPnlUSDT.toFixed(2))
        } 
    });

  } catch (error: any) {
    console.error("Erro interno na rota futures-positions:", error);
    return NextResponse.json({ success: false, message: "Falha interna ao buscar posições de futuros." }, { status: 500 });
  }
}
