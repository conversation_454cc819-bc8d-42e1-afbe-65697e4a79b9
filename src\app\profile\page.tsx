
"use client";

import { useState, useEffect, ChangeEvent, useRef } from "react";
import NextImage from "next/image"; // Renamed to avoid conflict if 'Image' is used elsewhere
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  UserCircle,
  Mail,
  Edit3,
  Smile,
  Bot,
  Rocket,
  Briefcase,
  ShieldCheck,
  Coffee,
  Gamepad2,
  Code2,
  Palette,
  UploadCloud,
  Trash2,
  Loader2,
  Save
} from "lucide-react";
import { cn } from "@/lib/utils";
import type { LucideIcon } from "lucide-react";
import { toast } from "@/hooks/use-toast";

interface ProfileIcon {
  name: string;
  IconComponent: LucideIcon;
}

const availableIcons: ProfileIcon[] = [
  { name: "UserCircle", IconComponent: User<PERSON>ircle },
  { name: "<PERSON>", IconComponent: Smile },
  { name: "<PERSON><PERSON>", IconComponent: <PERSON><PERSON> },
  { name: "Rocket", IconComponent: Rocket },
  { name: "Briefcase", IconComponent: Briefcase },
  { name: "ShieldCheck", IconComponent: ShieldCheck },
  { name: "Coffee", IconComponent: Coffee },
  { name: "Gamepad2", IconComponent: Gamepad2 },
  { name: "Code2", IconComponent: Code2 },
];

export default function ProfilePage() {
  const [fullName, setFullName] = useState("Satoshi Nakamoto");
  const [email, setEmail] = useState("<EMAIL>"); // Assuming email is static for now
  const [selectedIconName, setSelectedIconName] = useState<string>("UserCircle");
  const [uploadedAvatarUrl, setUploadedAvatarUrl] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const storedFullName = localStorage.getItem("userFullName");
    if (storedFullName) {
      setFullName(storedFullName);
    }
    const storedAvatar = localStorage.getItem("userUploadedAvatar");
    if (storedAvatar) {
      setUploadedAvatarUrl(storedAvatar);
      setSelectedIconName(''); 
    } else {
      const storedIcon = localStorage.getItem("userSelectedAvatarIcon");
      if (storedIcon && availableIcons.some(icon => icon.name === storedIcon)) {
        setSelectedIconName(storedIcon);
      } else {
        setSelectedIconName("UserCircle"); // Default if nothing valid is stored
      }
    }
  }, []);

  const handleIconSelect = (iconName: string) => {
    setSelectedIconName(iconName);
    setUploadedAvatarUrl(null); 
    localStorage.setItem("userSelectedAvatarIcon", iconName);
    localStorage.removeItem("userUploadedAvatar"); 
    toast({ title: "Avatar Icon Changed", description: `Your avatar is now the ${iconName} icon.` });
    window.dispatchEvent(new CustomEvent('profileUpdated')); 
  };

  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      if (file.size > 5 * 1024 * 1024) { 
        toast({
          title: "File Too Large",
          description: "Please select an image smaller than 5MB.",
          variant: "destructive",
        });
        return;
      }
      setSelectedFile(file);
      setPreviewUrl(URL.createObjectURL(file));
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      toast({ title: "No File Selected", description: "Please select an image to upload.", variant: "destructive" });
      return;
    }

    setIsUploading(true);
    const formData = new FormData();
    formData.append("profileImage", selectedFile);

    try {
      const response = await fetch("/api/upload/profile-image", {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (response.ok && result.success) {
        setUploadedAvatarUrl(result.imageUrl);
        localStorage.setItem("userUploadedAvatar", result.imageUrl);
        localStorage.removeItem("userSelectedAvatarIcon"); 
        setSelectedIconName(''); 
        toast({ title: "Avatar Uploaded!", description: "Your new profile picture has been set." });
        setSelectedFile(null);
        setPreviewUrl(null);
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
        window.dispatchEvent(new CustomEvent('profileUpdated')); 
      } else {
        throw new Error(result.message || "Failed to upload image.");
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "An unknown error occurred.";
      toast({
        title: "Upload Failed",
        description: errorMessage,
        variant: "destructive",
      });
      console.error("Upload error:", error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveCustomPhoto = () => {
    setUploadedAvatarUrl(null);
    setSelectedFile(null);
    setPreviewUrl(null);
    localStorage.removeItem("userUploadedAvatar");
    const defaultIcon = "UserCircle"; 
    setSelectedIconName(defaultIcon);
    localStorage.setItem("userSelectedAvatarIcon", defaultIcon);
    toast({ title: "Custom Photo Removed", description: "Your avatar has been reset." });
    window.dispatchEvent(new CustomEvent('profileUpdated')); 
  };

  const handleSaveChanges = () => {
    localStorage.setItem("userFullName", fullName);
    if (uploadedAvatarUrl) {
      localStorage.setItem("userUploadedAvatar", uploadedAvatarUrl);
      localStorage.removeItem("userSelectedAvatarIcon");
    } else {
      localStorage.setItem("userSelectedAvatarIcon", selectedIconName || "UserCircle");
      localStorage.removeItem("userUploadedAvatar");
    }
    toast({ title: "Profile Saved", description: "Your profile information has been updated." });
    window.dispatchEvent(new CustomEvent('profileUpdated')); 
  };

  const CurrentAvatarIconComponent = availableIcons.find(icon => icon.name === selectedIconName)?.IconComponent || UserCircle;
  const fallbackText = fullName ? fullName.substring(0, 2).toUpperCase() : "CP";

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold tracking-tight mb-2 flex items-center gap-2">
          <UserCircle className="h-8 w-8 text-primary" />
          My Profile
        </h1>
        <p className="text-muted-foreground">
          View and update your personal information and avatar.
        </p>
      </div>

      <Card className="card-interactive">
        <CardHeader>
          <div className="flex items-center gap-4">
            <Avatar className="h-20 w-20 border-2 border-primary bg-muted">
              {uploadedAvatarUrl ? (
                <>
                  <AvatarImage src={uploadedAvatarUrl} alt={fullName || 'User Avatar'} data-ai-hint="user photo" />
                  <AvatarFallback className="text-primary text-2xl font-semibold">
                    {(fullName || "U").substring(0, 1).toUpperCase()}
                  </AvatarFallback>
                </>
              ) : (
                <>
                  <CurrentAvatarIconComponent className="h-full w-full p-3 text-primary" />
                  {/* Show fallback text only if no specific icon is chosen (i.e., selectedIconName is empty or it's the default UserCircle) */}
                  {(!selectedIconName || selectedIconName === "UserCircle") && (
                    <AvatarFallback className="text-primary text-2xl font-semibold">
                      {fallbackText}
                    </AvatarFallback>
                  )}
                </>
              )}
            </Avatar>
            <div>
              <CardTitle className="text-2xl">{fullName}</CardTitle>
              <CardDescription className="flex items-center gap-1 mt-1">
                <Mail className="h-4 w-4 text-muted-foreground" />
                {email}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6 pt-6">
          <div className="space-y-2">
            <Label htmlFor="fullName">Full Name</Label>
            <Input id="fullName" value={fullName} onChange={(e) => setFullName(e.target.value)} />
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <Input id="email" type="email" value={email} readOnly disabled />
             <p className="text-xs text-muted-foreground">Email cannot be changed.</p>
          </div>

          <div className="flex justify-between items-center">
            <Button variant="outline">
                <Edit3 className="mr-2 h-4 w-4"/>
                Change Password
            </Button>
            <Button onClick={handleSaveChanges} className="bg-accent hover:bg-accent/90 text-accent-foreground">
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card className="card-interactive">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UploadCloud className="h-5 w-5 text-primary" />
            Upload Custom Photo
          </CardTitle>
          <CardDescription>
            Choose an image file from your device (max 5MB).
            {uploadedAvatarUrl && (
              <span className="block mt-1 text-sm text-green-500">A custom photo is currently active.</span>
            )}
            </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Input
            id="profileImageUpload"
            type="file"
            accept="image/png, image/jpeg, image/gif, image/webp"
            onChange={handleFileChange}
            ref={fileInputRef}
            className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary/10 file:text-primary hover:file:bg-primary/20"
          />
          {previewUrl && (
            <div className="mt-4 border p-2 rounded-md bg-muted/50 inline-block">
              <p className="text-sm text-muted-foreground mb-2">Preview:</p>
              <NextImage src={previewUrl} alt="Selected image preview" width={128} height={128} className="rounded-md object-cover aspect-square" data-ai-hint="image preview" />
            </div>
          )}
          <div className="flex items-center gap-2">
            <Button onClick={handleUpload} disabled={isUploading || !selectedFile}>
              {isUploading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <UploadCloud className="mr-2 h-4 w-4" />
              )}
              {isUploading ? "Uploading..." : "Upload Image"}
            </Button>
            {uploadedAvatarUrl && (
              <Button variant="outline" onClick={handleRemoveCustomPhoto} disabled={isUploading}>
                <Trash2 className="mr-2 h-4 w-4" />
                Remove Custom Photo
              </Button>
            )}
          </div>
        </CardContent>
      </Card>


      <Card className="card-interactive">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5 text-primary" />
            Choose Your Avatar Icon
          </CardTitle>
          <CardDescription>Or select an icon to represent you on the platform.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-4 sm:grid-cols-5 md:grid-cols-6 lg:grid-cols-8 gap-4">
            {availableIcons.map(({ name, IconComponent }) => (
              <Button
                key={name}
                variant="outline"
                className={cn(
                  "p-2 h-16 w-16 flex items-center justify-center aspect-square transition-all duration-150 ease-in-out",
                  "hover:bg-accent/20 hover:border-primary",
                  selectedIconName === name && !uploadedAvatarUrl && "ring-2 ring-primary border-primary bg-primary/10"
                )}
                onClick={() => handleIconSelect(name)}
                aria-label={`Select ${name} avatar`}
                disabled={isUploading}
              >
                <IconComponent className={cn("h-8 w-8", (selectedIconName === name && !uploadedAvatarUrl) ? "text-primary" : "text-muted-foreground group-hover:text-foreground")} />
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

       <Card className="card-interactive">
        <CardHeader>
          <CardTitle>Account Statistics</CardTitle>
          <CardDescription>Overview of your platform activity.</CardDescription>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 bg-muted/50 rounded-lg text-center">
            <p className="text-sm text-muted-foreground">Total Trades</p>
            <p className="text-2xl font-semibold">128</p>
          </div>
          <div className="p-4 bg-muted/50 rounded-lg text-center">
            <p className="text-sm text-muted-foreground">Active Alerts</p>
            <p className="text-2xl font-semibold">5</p>
          </div>
          <div className="p-4 bg-muted/50 rounded-lg text-center">
            <p className="text-sm text-muted-foreground">Member Since</p>
            <p className="text-2xl font-semibold">Jan 2023</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

    