import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import Binance from 'node-binance-api';
import type { Transaction } from '@/lib/types';

// Helper to get full crypto name (simplified for prototype)
const getCryptoName = (symbol: string): string => {
  const map: Record<string, string> = {
    BTC: 'Bitcoin',
    ETH: 'Ethereum',
    BNB: 'BNB',
    SOL: 'Solana',
    ADA: 'Cardano',
    XRP: 'XRP',
    DOT: 'Polkadot',
    DOGE: 'Dogecoin',
    // Add more common ones
  };
  return map[symbol] || symbol;
};


export async function POST(request: NextRequest) {
  try {
    let body;
    try {
      body = await request.json();
    } catch (parseError) {
      console.error("Erro ao fazer parse do JSON da requisição:", parseError);
      return NextResponse.json({ success: false, message: "Corpo da requisição inválido ou ausente." }, { status: 400 });
    }
    
    const { apiKey, apiSecret } = body;

    if (!apiKey || !apiSecret) {
      return NextResponse.json({ success: false, message: "API Key ou Secret ausentes." }, { status: 400 });
    }

    const binance = new Binance().options({
      APIKEY: apiKey,
      APISECRET: apiSecret,
      family: 4,
    });

    // For prototype, fetch for a few common pairs
    const tradeSymbolsToFetch = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'ADAUSDT']; // Add more or make dynamic if needed
    const allTrades: Transaction[] = [];
    const tradeFetchLimit = 10; // Fetch last 10 trades per symbol

    for (const symbol of tradeSymbolsToFetch) {
      try {
        // Try futures trades first, then spot trades if needed
        let trades: any[] = [];
        
        try {
          // For futures trades, use futuresUserTrades
          trades = await binance.futuresUserTrades(symbol, { limit: tradeFetchLimit });
        } catch (futuresError: any) {
          console.log(`Futures trades not available for ${symbol}, trying spot trades...`);
          try {
            // For spot trades, use myTrades method (correct method name)
            trades = await binance.myTrades(symbol, { limit: tradeFetchLimit });
          } catch (spotError: any) {
            console.warn(`Both futures and spot trades failed for ${symbol}:`, spotError.message);
            continue;
          }
        }
        
        if (Array.isArray(trades) && trades.length > 0) {
          trades.forEach((trade: any) => {
            const baseAsset = symbol.replace(/(USDT|BUSD|BTC|ETH)$/, ''); // Basic way to get base asset
            
            // Determine trade type (buy/sell) - different APIs use different properties
            let tradeType: 'buy' | 'sell' = 'sell'; // default fallback
            
            if (trade.isBuyer !== undefined) {
              // Spot trades use isBuyer
              tradeType = trade.isBuyer ? 'buy' : 'sell';
            } else if (trade.side !== undefined) {
              // Futures trades use side
              tradeType = trade.side.toLowerCase() === 'buy' ? 'buy' : 'sell';
            } else if (trade.buyer !== undefined) {
              // Alternative property name
              tradeType = trade.buyer ? 'buy' : 'sell';
            }
            
            allTrades.push({
              id: String(trade.id || trade.orderId + '_' + trade.time), // Ensure unique ID
              type: tradeType,
              cryptoSymbol: baseAsset,
              cryptoName: getCryptoName(baseAsset),
              amount: parseFloat(trade.qty || trade.quantity || '0'),
              pricePerUnit: parseFloat(trade.price || '0'),
              totalValue: parseFloat(trade.price || '0') * parseFloat(trade.qty || trade.quantity || '0'),
              date: new Date(trade.time || Date.now()).toISOString(),
              exchange: 'Binance',
            });
          });
        }
      } catch (e: any) {
        // Log error for individual symbol but continue
        console.warn(`Erro ao buscar trades para ${symbol}:`, e.body ? (typeof e.body === 'string' ? JSON.parse(e.body).msg : e.body.msg) : e.message);
      }
    }

    // Sort all collected trades by date descending and take the most recent (e.g., 20)
    allTrades.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    const recentTransactions = allTrades.slice(0, 25); // Show up to 25 most recent transactions overall

    return NextResponse.json({ success: true, data: { transactions: recentTransactions } });

  } catch (error: any) {
    console.error("Erro interno na rota recent-trades:", error);
    // Check if error is from Binance API (e.g. invalid keys)
    if (error && error.body && typeof error.body === 'string') {
        try {
            const errorBody = JSON.parse(error.body);
            if (errorBody.msg) {
                 return NextResponse.json({ success: false, message: `Erro da API Binance: ${errorBody.msg}` }, { status: 401 });
            }
        } catch (parseError) { /* Ignore parse error, fall through to generic */ }
    }
    return NextResponse.json({ success: false, message: "Falha interna ao buscar trades recentes." }, { status: 500 });
  }
}
