# 📈 GGO - Trading Application

Uma aplicação de trading completa construída com Next.js, integrada com a API da Binance para operações reais de futuros e spot.

## 🚀 Funcionalidades Principais

### 📊 Dashboard Completo
- **Visão Geral da Carteira**: Valor total do portfólio, saldo spot e futuros
- **P&L em Tempo Real**: Acompanhamento de lucros/perdas nas últimas 24h
- **Posições Abertas**: Monitoramento completo de posições de futuros
- **Histórico de Transações**: Lista das operações mais recentes
- **Ativos**: Visualização detalhada da carteira de criptomoedas

### 🔔 Sistema de Alertas de Preço
- Criação de alertas para preços acima ou abaixo de valores específicos
- Notificações automáticas quando os preços atingem os alvos
- Gerenciamento completo (ativar/desativar/excluir alertas)

### 💼 Terminal de Trading de Futuros
- **Ordens de Mercado e Limite**: Suporte completo para ambos os tipos
- **Alavancagem Configurável**: 1x até 125x
- **Cálculo Automático**: Margem estimada e valor nocional mínimo
- **Validação Inteligente**: Verificação automática dos filtros da Binance
- **Auto-preenchimento**: Valor mínimo nocional calculado automaticamente

### ⚙️ Gerenciamento de Posições (NOVO!)
- **Fechar Posições**: Fechamento real de posições com um clique
- **Stop Loss/Take Profit**: Configuração de ordens de proteção automáticas
- **Validação Inteligente**: Verificação de preços válidos baseado no tipo de posição
- **Atualização em Tempo Real**: Interface atualizada automaticamente após operações

## 🛠️ Tecnologias Utilizadas

- **Frontend**: Next.js 14, React, TypeScript
- **UI Components**: Radix UI, Tailwind CSS
- **Validação**: Zod, React Hook Form
- **API Integration**: Binance API (node-binance-api)
- **Notificações**: React Hot Toast

## 📦 Instalação

1. Clone o repositório:
```bash
git clone <repo-url>
cd ggo
```

2. Instale as dependências:
```bash
npm install
```

3. Execute o servidor de desenvolvimento:
```bash
npm run dev
```

4. Acesse [http://localhost:9002](http://localhost:9002) no seu navegador

## ⚠️ Configuração da API

1. Vá para **Configurações** na aplicação
2. Insira suas chaves de API da Binance:
   - **API Key**: Sua chave pública da Binance
   - **Secret Key**: Sua chave secreta da Binance

### Permissões Necessárias da API:
- ✅ **Spot Trading**: Para operações spot
- ✅ **Futures Trading**: Para operações de futuros
- ✅ **Read Info**: Para leitura de dados da conta

## 🔧 APIs Implementadas

### Posições e Conta
- `GET /api/binance/account-overview` - Visão geral da conta
- `POST /api/binance/futures-positions` - Posições de futuros abertas
- `POST /api/binance/recent-trades` - Histórico de trades recentes

### Trading
- `POST /api/binance/futures/place-order` - Colocar ordens de futuros
- `POST /api/binance/futures/close-position` - **NOVO!** Fechar posições
- `POST /api/binance/futures/modify-position` - **NOVO!** Configurar SL/TP

### Informações de Mercado
- `GET /api/binance/symbol-info` - Informações de símbolos
- `GET /api/binance/exchange-info` - Informações da exchange
- `GET /api/binance/current-prices` - Preços atuais

## 🔥 Funcionalidades Implementadas Recentemente

### Stop Loss/Take Profit
- **Interface Intuitiva**: Modal dedicado para configuração de SL/TP
- **Validação Automática**: Verificação de preços válidos por tipo de posição
- **Operações Reais**: Criação de ordens condicionais na Binance
- **Cancelamento Inteligente**: Remove ordens SL/TP existentes antes de criar novas

### Fechamento de Posições
- **Um Clique**: Fechamento instantâneo de posições
- **Ordem de Mercado**: Execução imediata ao preço atual
- **Confirmação Segura**: Dialog de confirmação com P&L atual
- **Limpeza Automática**: Remove ordens SL/TP relacionadas

## 📋 Validações e Proteções

### Terminal de Trading
- **Valor Mínimo Nocional**: Cálculo automático baseado nos filtros da Binance
- **Precisão de Quantidade**: Ajuste automático para `stepSize`
- **Precisão de Preço**: Ajuste automático para `tickSize` (ordens limite)
- **Margem Mínima**: Validação do saldo disponível

### Stop Loss/Take Profit
- **Direção Correta**: SL abaixo/acima do preço atual conforme tipo de posição
- **Take Profit Lógico**: TP acima/abaixo do preço atual conforme tipo de posição
- **Preços Válidos**: Verificação de valores positivos e formato correto

## 🛡️ Segurança

- **Chaves Locais**: API keys armazenadas apenas no localStorage
- **Validação Dupla**: Frontend e backend validam todos os parâmetros
- **Operações Reais**: Avisos claros sobre operações que afetam fundos reais
- **Confirmações**: Dialogs de confirmação para operações críticas

## 🚨 Avisos Importantes

### ⚠️ **OPERAÇÕES REAIS**
Esta aplicação executa **OPERAÇÕES REAIS** na Binance. Use com extrema cautela:

- ✅ Teste primeiro no **Testnet da Binance**
- ✅ Use valores pequenos inicialmente
- ✅ Entenda completamente os riscos
- ✅ Verifique todas as configurações

### 💰 **Gestão de Risco**
- Configure sempre Stop Loss em posições de futuros
- Use alavancagem moderada
- Monitore suas posições regularmente
- Tenha um plano de trading definido

## 🔧 Estrutura do Projeto

```
src/
├── app/
│   ├── api/binance/          # APIs da Binance
│   ├── settings/             # Página de configurações
│   └── page.tsx              # Dashboard principal
├── components/
│   ├── dashboard/            # Componentes do dashboard
│   ├── trading/              # Terminal e modais de trading
│   └── ui/                   # Componentes de UI
└── lib/
    └── types.ts              # Definições de tipos
```

## 📝 Changelog v2.0

### ✨ Novas Funcionalidades
- **Stop Loss/Take Profit**: Sistema completo de ordens condicionais
- **Fechar Posições**: Fechamento com um clique
- **Modal de Modificação**: Interface dedicada para SL/TP
- **APIs de Posições**: Endpoints para fechar e modificar posições

### 🐛 Correções
- Corrigido erro `binance.myTrades is not a function`
- Melhorado sistema de validação de ordens
- Corrigido cálculo de valor mínimo nocional
- Aprimorada detecção de sucesso em ordens

### 🚀 Melhorias
- Interface mais intuitiva para gerenciamento de posições
- Feedback visual melhorado com loading states
- Validações mais robustas
- Documentação atualizada

---

**Desenvolvido com ❤️ para traders que buscam precisão e confiabilidade.**
