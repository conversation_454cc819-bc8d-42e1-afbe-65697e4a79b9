
import { AssetTable } from "@/components/dashboard/AssetTable";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import type { PortfolioAsset } from "@/lib/types";
import { PortfolioSummaryCard } from "@/components/dashboard/PortfolioSummaryCard";
import { DollarSign, Percent, BarChartBig } from "lucide-react";

// Mock assets now do not need iconUrl, AssetTable will handle it.
const mockAssets: PortfolioAsset[] = [
  { id: '1', name: 'Bitcoin', symbol: 'BTC', quantity: 0.5, price: 60000, value: 30000, change24h: 2.5, exchange: 'Binance' },
  { id: '2', name: 'Ethereum', symbol: 'ETH', quantity: 10, price: 3000, value: 30000, change24h: -1.2, exchange: 'Kraken' },
  { id: '3', name: 'Solana', symbol: 'SOL', quantity: 100, price: 150, value: 15000, change24h: 5.8, exchange: 'Coinbase' },
  { id: '4', name: 'Cardano', symbol: 'ADA', quantity: 5000, price: 0.45, value: 2250, change24h: 1.0, exchange: 'Binance' },
  { id: '5', name: 'Polkadot', symbol: 'DOT', quantity: 200, price: 7.20, value: 1440, change24h: -0.5, exchange: 'Kraken' },
];

export default function PortfolioPage() {
  const totalPortfolioValue = mockAssets.reduce((sum, asset) => sum + asset.value, 0);
  const average24hChange = mockAssets.reduce((sum, asset) => sum + asset.change24h, 0) / (mockAssets.length || 1);

  return (
    <div className="flex flex-col gap-6">
      <Card className="bg-card text-card-foreground border-border shadow-sm"> {/* Basic card for title, not interactive */}
        <CardHeader>
          <CardTitle className="text-3xl">Aggregated Portfolio</CardTitle>
          <CardDescription>A detailed view of all your assets across connected exchanges.</CardDescription>
        </CardHeader>
      </Card>
      
      <div className="grid gap-4 md:grid-cols-3">
        <PortfolioSummaryCard 
          title="Total Value" 
          value={`$${totalPortfolioValue.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
          icon={DollarSign}
        />
        <PortfolioSummaryCard 
          title="Total Assets" 
          value={mockAssets.length.toString()}
          icon={BarChartBig}
        />
        <PortfolioSummaryCard 
          title="Avg. 24h Change" 
          value={`${average24hChange.toFixed(2)}%`}
          icon={Percent}
          iconColor={average24hChange >= 0 ? "text-green-500" : "text-red-500"}
        />
      </div>

      <Card className="card-interactive">
        <CardHeader>
          <CardTitle>All Assets</CardTitle>
          <CardDescription>Detailed breakdown of each asset in your portfolio.</CardDescription>
        </CardHeader>
        <CardContent>
          <AssetTable assets={mockAssets} />
        </CardContent>
      </Card>
    </div>
  );
}
