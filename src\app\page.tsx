
"use client";

import { useEffect, useState, useRef } from "react";
import { PortfolioSummaryCard } from "@/components/dashboard/PortfolioSummaryCard";
import { AssetTable } from "@/components/dashboard/AssetTable";
import { TransactionHistoryTable } from "@/components/dashboard/TransactionHistoryTable";
import { OpenPositionsTable } from "@/components/dashboard/OpenPositionsTable";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import type { PortfolioAsset, Transaction, OpenPosition, PriceAlert } from "@/lib/types";
import { DollarSign, TrendingUp, ListCollapse, Activity, AlertTriangle, Loader2, Info, Briefcase, BellRing, Wallet, ServerCrash, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { CreateAlertForm } from "@/components/alerts/CreateAlertForm";
import { ActiveAlertsList } from "@/components/alerts/ActiveAlertsList";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/hooks/use-toast";
import { FuturesTradingTerminal } from "@/components/trading/FuturesTradingTerminal";
import { cn } from "@/lib/utils";

interface AccountOverviewData {
  totalPortfolioValueUSDT: number;
  totalSpotValueUSDT: number;
  totalFuturesValueUSDT: number;
  spotPnl24hUSDT: number;
  spotPnl24hPercentage: number;
  assets: PortfolioAsset[];
  allPrices?: Record<string, string>;
}

interface CryptoInfo {
  symbol: string;
  name: string;
}

const DASHBOARD_REFRESH_INTERVAL = 5000; // Refresh main dashboard data every 5 seconds

export default function DashboardPage() {
  const [apiKey, setApiKey] = useState<string | null>(null);
  const [apiSecret, setApiSecret] = useState<string | null>(null);
  const [isKeysLoading, setIsKeysLoading] = useState(true);

  const [accountOverview, setAccountOverview] = useState<AccountOverviewData | null>(null);
  const [isLoadingAccountOverview, setIsLoadingAccountOverview] = useState(true);
  const [accountOverviewError, setAccountOverviewError] = useState<string | null>(null);

  const [openPositions, setOpenPositions] = useState<OpenPosition[]>([]);
  const [totalUnrealizedFuturesPnl, setTotalUnrealizedFuturesPnl] = useState<number | null>(null);
  const [isLoadingOpenPositions, setIsLoadingOpenPositions] = useState(true);
  const [openPositionsError, setOpenPositionsError] = useState<string | null>(null);

  const [recentTransactions, setRecentTransactions] = useState<Transaction[]>([]);
  const [isLoadingTransactions, setIsLoadingTransactions] = useState(true);
  const [transactionsError, setTransactionsError] = useState<string | null>(null);

  const [alerts, setAlerts] = useState<PriceAlert[]>([]);
  const [cryptocurrencies, setCryptocurrencies] = useState<CryptoInfo[]>([]);
  const [isLoadingCryptos, setIsLoadingCryptos] = useState(true);
  const [errorCryptos, setErrorCryptos] = useState<string | null>(null);
  const [activeAlertsCount, setActiveAlertsCount] = useState<number>(0);
  const alertsSectionRef = useRef<HTMLDivElement>(null);
  const [currentPrices, setCurrentPrices] = useState<Record<string, string>>({});

  const dashboardRefreshIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const storedApiKey = localStorage.getItem("binanceApiKey");
    const storedApiSecret = localStorage.getItem("binanceApiSecret");
    setApiKey(storedApiKey);
    setApiSecret(storedApiSecret);
    setIsKeysLoading(false);
  }, []);

  const fetchAccountOverview = async () => {
    if (!apiKey || !apiSecret) {
      if (!isKeysLoading) setAccountOverviewError("Chaves de API da Binance não configuradas.");
      setIsLoadingAccountOverview(false);
      return;
    }
    if (!accountOverview) setIsLoadingAccountOverview(true); 
    setAccountOverviewError(null);

    try {
      const response = await fetch('/api/binance/account-overview', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ apiKey, apiSecret }),
      });
       if (!response.ok) {
        let serverMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorResult = await response.json();
          if (errorResult && errorResult.message) {
            serverMessage = errorResult.message;
          }
        } catch (e) {
          serverMessage = response.statusText || serverMessage;
        }
        throw new Error(serverMessage);
      }
      const result = await response.json();
      if (result.success && result.data && result.data.accountOverviewData) {
        setAccountOverview(result.data.accountOverviewData);
        if (result.data.accountOverviewData.allPrices) {
          setCurrentPrices(result.data.accountOverviewData.allPrices);
        }
        setAccountOverviewError(null); 
      } else {
        setAccountOverviewError(result.message || `Falha ao buscar dados da visão geral da conta.`);
        setCurrentPrices({});
      }
    } catch (err: any) {
      setAccountOverviewError(`Erro ao buscar visão geral: ${err.message}`);
      setCurrentPrices({});
      console.error(`Error fetching account overview:`, err);
    } finally {
      setIsLoadingAccountOverview(false);
    }
  };

  const fetchOpenPositions = async () => {
    if (!apiKey || !apiSecret) {
        if (!isKeysLoading) setOpenPositionsError("Chaves de API da Binance não configuradas.");
        if (openPositions.length === 0) setIsLoadingOpenPositions(false);
        return;
    }
    if (openPositions.length === 0) setIsLoadingOpenPositions(true); 
    setOpenPositionsError(null);
    try {
        const response = await fetch('/api/binance/futures-positions', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ apiKey, apiSecret }),
        });
        if (!response.ok) {
          let serverMessage = `HTTP error! status: ${response.status}`;
          try {
            const errorResult = await response.json();
            if (errorResult && errorResult.message) {
              serverMessage = errorResult.message;
            }
          } catch (e) {
            serverMessage = response.statusText || serverMessage;
          }
          throw new Error(serverMessage);
        }
        const result = await response.json();
        if (result.success && result.data) {
            setOpenPositions(result.data.positions || []);
            setTotalUnrealizedFuturesPnl(result.data.totalUnrealizedFuturesPnlUSDT !== undefined ? result.data.totalUnrealizedFuturesPnlUSDT : 0);
            setOpenPositionsError(null); 
        } else {
            setOpenPositionsError(result.message || `Falha ao buscar posições de futuros.`);
            setOpenPositions([]);
            setTotalUnrealizedFuturesPnl(0);
        }
    } catch (err: any) {
        setOpenPositionsError(`Erro ao buscar posições: ${err.message}`);
        setOpenPositions([]);
        setTotalUnrealizedFuturesPnl(0);
        console.error(`Error fetching open positions:`, err);
    } finally {
       setIsLoadingOpenPositions(false); 
    }
  };

  const fetchRecentTransactions = async () => {
     if (!apiKey || !apiSecret) {
      if (!isKeysLoading) setTransactionsError("Chaves de API da Binance não configuradas.");
      if (recentTransactions.length === 0) setIsLoadingTransactions(false);
      return;
    }
    if (recentTransactions.length === 0) setIsLoadingTransactions(true); 
    setTransactionsError(null);
    try {
      const response = await fetch('/api/binance/recent-trades', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ apiKey, apiSecret }),
      });
      if (!response.ok) {
        let serverMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorResult = await response.json();
          if (errorResult && errorResult.message) {
            serverMessage = errorResult.message;
          }
        } catch (e) {
          serverMessage = response.statusText || serverMessage;
        }
        throw new Error(serverMessage);
      }
      const result = await response.json();
      if (result.success && result.data && result.data.transactions) {
        setRecentTransactions(result.data.transactions);
        setTransactionsError(null); 
      } else {
        setTransactionsError(result.message || `Falha ao buscar transações recentes (dados inválidos).`);
        setRecentTransactions([]);
      }
    } catch (err: any) {
      setTransactionsError(`Erro ao buscar transações: ${err.message}`);
      setRecentTransactions([]);
      console.error(`Error fetching recent transactions:`, err);
    } finally {
      setIsLoadingTransactions(false); 
    }
  };

   const fetchCryptos = async () => {
    if (!apiKey || !apiSecret) {
      setIsLoadingCryptos(false);
      if (!isKeysLoading) setErrorCryptos("Configure as chaves de API nas Configurações para habilitar a criação de alertas e negociação.");
      setCryptocurrencies([]);
      return;
    }
    setIsLoadingCryptos(true);
    setErrorCryptos(null);
    try {
      const response = await fetch('/api/binance/exchange-info');
       if (!response.ok) {
        let serverMessage = `HTTP error! status: ${response.status}`;
        try {
          const errorResult = await response.json();
          if (errorResult && errorResult.message) {
            serverMessage = errorResult.message;
          }
        } catch (e) {
            serverMessage = response.statusText || serverMessage;
        }
        throw new Error(serverMessage);
      }
      const result = await response.json();
      if (result.success && Array.isArray(result.data)) {
        const filteredCryptos = result.data.filter((c: CryptoInfo) => {
          const upperSymbol = c.symbol.toUpperCase();
          return !['USDT', 'BUSD', 'USDC', 'DAI', 'TUSD', 'PAX', 'EUR', 'GBP', 'AUD'].includes(upperSymbol) &&
                 !upperSymbol.endsWith('UP') && !upperSymbol.endsWith('DOWN') && !upperSymbol.endsWith('BEAR') && !upperSymbol.endsWith('BULL');
        }).map((c: CryptoInfo) => ({ ...c, name: c.symbol }));
        setCryptocurrencies(filteredCryptos);
        setErrorCryptos(null); 
      } else {
        throw new Error(result.message || "Failed to fetch cryptocurrency list.");
      }
    } catch (error: any) {
      console.error("Error fetching cryptos:", error);
      const errorMessage = error instanceof Error ? error.message : "Unknown error fetching crypto list.";
      setErrorCryptos(errorMessage);
      setCryptocurrencies([]);
    } finally {
      setIsLoadingCryptos(false);
    }
  };


  useEffect(() => {
    if (apiKey && apiSecret) {
      fetchAccountOverview();
      fetchOpenPositions();
      fetchRecentTransactions();
      fetchCryptos(); 

      if (dashboardRefreshIntervalRef.current) {
        clearInterval(dashboardRefreshIntervalRef.current);
      }
      dashboardRefreshIntervalRef.current = setInterval(() => {
        fetchAccountOverview(); 
        fetchOpenPositions();
        fetchRecentTransactions();
      }, DASHBOARD_REFRESH_INTERVAL);

    } else {
      setAccountOverview(null);
      setOpenPositions([]);
      setTotalUnrealizedFuturesPnl(null);
      setRecentTransactions([]);
      setCryptocurrencies([]);
      setCurrentPrices({});
      setAlerts([]); 

      if (!isKeysLoading) {
        setAccountOverviewError("Chaves de API não configuradas.");
        setOpenPositionsError("Chaves de API não configuradas.");
        setTransactionsError("Chaves de API não configuradas.");
        setErrorCryptos("Configure as chaves de API nas Configurações para habilitar funcionalidades.");
      }
      if (dashboardRefreshIntervalRef.current) {
        clearInterval(dashboardRefreshIntervalRef.current);
      }
    }
    return () => {
      if (dashboardRefreshIntervalRef.current) {
        clearInterval(dashboardRefreshIntervalRef.current);
      }
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [apiKey, apiSecret, isKeysLoading]); 

  useEffect(() => {
    const storedAlerts = localStorage.getItem("priceAlerts");
    if (storedAlerts) {
      try {
        const parsedAlerts: PriceAlert[] = JSON.parse(storedAlerts);
        setAlerts(parsedAlerts);
      } catch (e) {
        console.error("Failed to parse alerts from localStorage", e);
        setAlerts([]);
      }
    }
  }, []);

  useEffect(() => {
    localStorage.setItem("priceAlerts", JSON.stringify(alerts));
    setActiveAlertsCount(alerts.filter(a => a.status === 'active').length);
  }, [alerts]);


  useEffect(() => {
    const activeAlertsToCheck = alerts.filter(alert => alert.status === 'active');

    if (!apiKey || !apiSecret || activeAlertsToCheck.length === 0 || Object.keys(currentPrices).length === 0) {
        return;
    }

    let updatedAlertsOccurred = false;
    const newAlertsState = alerts.map(alert => {
        if (alert.status === 'active') {
            const tradingPair = `${alert.cryptoSymbol.toUpperCase()}USDT`; 
            const currentPriceStr = currentPrices[tradingPair];

            if (currentPriceStr) {
                const currentPriceNum = parseFloat(currentPriceStr);
                if (!isNaN(currentPriceNum)) {
                    const conditionMet =
                        (alert.condition === 'above' && currentPriceNum > alert.targetPrice) ||
                        (alert.condition === 'below' && currentPriceNum < alert.targetPrice);

                    if (conditionMet) {
                        updatedAlertsOccurred = true;
                        toast({
                            title: "🚀 Alerta Acionado!",
                            description: `${alert.cryptoName} (${alert.cryptoSymbol}) está ${alert.condition === 'above' ? 'acima de' : 'abaixo de'} $${alert.targetPrice.toLocaleString()}. Preço Atual: $${currentPriceNum.toLocaleString()}`,
                            variant: "default",
                            duration: 10000,
                        });
                        return {
                            ...alert,
                            status: 'triggered' as 'triggered',
                            triggeredAt: new Date().toISOString(),
                            triggeredPrice: currentPriceNum,
                        };
                    }
                }
            }
        }
        return alert;
    });

    if (updatedAlertsOccurred) {
        setAlerts(newAlertsState);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPrices, alerts, apiKey, apiSecret]); 


  const handleAlertCreated = (newAlertData: { cryptoSymbol: string, condition: 'above' | 'below', targetPrice: number }) => {
    const cryptoDetail = cryptocurrencies.find(c => c.symbol === newAlertData.cryptoSymbol);
    const newAlert: PriceAlert = {
      id: `alert${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      cryptoSymbol: newAlertData.cryptoSymbol,
      cryptoName: cryptoDetail?.name || newAlertData.cryptoSymbol,
      condition: newAlertData.condition,
      targetPrice: newAlertData.targetPrice,
      createdAt: new Date().toISOString(),
      status: 'active',
      triggeredAt: null,
      triggeredPrice: null,
    };
    setAlerts(prevAlerts => [newAlert, ...prevAlerts].sort((a, b) => b.createdAt.localeCompare(a.createdAt)));
  };

  const handleDeleteAlert = (alertId: string) => {
    setAlerts(prevAlerts => prevAlerts.filter(alert => alert.id !== alertId));
    toast({
      title: "Alerta Excluído",
      description: "O alerta de preço foi removido com sucesso.",
      variant: "destructive"
    });
  };

  const handleToggleAlertStatus = (alertId: string, currentStatus: PriceAlert['status']) => {
    setAlerts(prevAlerts =>
      prevAlerts.map(alert => {
        if (alert.id === alertId) {
          const newStatus = (currentStatus === 'active' || currentStatus === 'triggered') ? 'cancelled' : 'active';
          return {
            ...alert,
            status: newStatus,
            triggeredAt: (newStatus === 'active') ? null : alert.triggeredAt, 
            triggeredPrice: (newStatus === 'active') ? null : alert.triggeredPrice,
          };
        }
        return alert;
      }).sort((a, b) => b.createdAt.localeCompare(a.createdAt))
    );
    toast({
      title: `Alerta ${currentStatus === 'active' || currentStatus === 'triggered' ? 'Desativado' : 'Ativado'}`,
      description: `O status do alerta de preço foi atualizado.`,
    });
  };

  const handleScrollToAlerts = () => {
    alertsSectionRef.current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
  };

  const handleManualRefresh = () => {
    if (apiKey && apiSecret) {
        toast({ title: "Atualizando Dados...", description: "Buscando as informações mais recentes da Binance.", duration: 2000});
        setIsLoadingAccountOverview(true);
        setIsLoadingOpenPositions(true);
        setIsLoadingTransactions(true);
        
        fetchAccountOverview();
        fetchOpenPositions();
        fetchRecentTransactions();
    } else {
        toast({ title: "Chaves de API Ausentes", description: "Configure suas chaves de API para atualizar os dados.", variant: "destructive"});
    }
  }


  if (isKeysLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
      </div>
    );
  }

  if (!apiKey || !apiSecret) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center space-y-4 p-8">
        <AlertTriangle className="h-16 w-16 text-amber-500" />
        <h2 className="text-2xl font-semibold">API da Binance Não Configurada</h2>
        <p className="text-muted-foreground">
          Por favor, configure suas chaves de API da Binance na página de <Link href="/settings" className="text-primary hover:underline">Configurações</Link> para visualizar seu dashboard e utilizar todas as funcionalidades.
        </p>
        <Button asChild>
          <Link href="/settings">Ir para Configurações</Link>
        </Button>
      </div>
    );
  }

  const isAnyDataLoading = isLoadingAccountOverview || isLoadingOpenPositions || isLoadingTransactions;

  const isTotalValueCardLoading = isLoadingAccountOverview && !accountOverview;
  const totalValueDisplay = isTotalValueCardLoading
    ? "..."
    : accountOverviewError
    ? "Erro"
    : `$${accountOverview?.totalPortfolioValueUSDT.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) ?? '0.00'}`;

  const totalValueChangeText = isTotalValueCardLoading || accountOverviewError ? "" : "API Conectada";

  const isSpotValueCardLoading = isLoadingAccountOverview && !accountOverview;
  const totalSpotValueDisplay = isSpotValueCardLoading
    ? "..."
    : accountOverviewError
    ? "Erro"
    : `$${accountOverview?.totalSpotValueUSDT?.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) ?? '0.00'}`;

  const isFuturesWalletCardLoading = isLoadingAccountOverview && !accountOverview;
  const totalFuturesWalletDisplay = isFuturesWalletCardLoading
    ? "..."
    : accountOverviewError
    ? "Erro"
    : `$${accountOverview?.totalFuturesValueUSDT?.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) ?? '0.00'}`;

  let spotPnlValueDisplay;
  if (isSpotValueCardLoading) {
    spotPnlValueDisplay = "...";
  } else if (accountOverviewError) {
    spotPnlValueDisplay = "Erro";
  } else if (accountOverview && typeof accountOverview.spotPnl24hUSDT === 'number') {
    const pnl = accountOverview.spotPnl24hUSDT;
    const formattedPnl = pnl.toLocaleString('en-US', { style: 'currency', currency: 'USD', minimumFractionDigits: 2, maximumFractionDigits: 2 });
    if (pnl > 0) {
      spotPnlValueDisplay = `+${formattedPnl}`;
    } else {
      spotPnlValueDisplay = formattedPnl;
    }
  } else {
    spotPnlValueDisplay = (0).toLocaleString('en-US', { style: 'currency', currency: 'USD', minimumFractionDigits: 2, maximumFractionDigits: 2 });
  }

  const spotPnlChangeText = isSpotValueCardLoading || accountOverviewError
    ? ""
    : (accountOverview && typeof accountOverview.spotPnl24hPercentage === 'number' ? `${accountOverview.spotPnl24hPercentage.toFixed(1)}% nas últimas 24h` : "0.0% nas últimas 24h");

  const isFuturesPnlCardLoading = (isLoadingOpenPositions && totalUnrealizedFuturesPnl === null);
  let futuresPnlValueDisplay;
  if (isFuturesPnlCardLoading) {
      futuresPnlValueDisplay = "...";
  } else if (openPositionsError) {
    futuresPnlValueDisplay = "Erro";
  } else if (typeof totalUnrealizedFuturesPnl === 'number') {
    const pnl = totalUnrealizedFuturesPnl;
    const formattedPnl = pnl.toLocaleString('en-US', { style: 'currency', currency: 'USD', minimumFractionDigits: 2, maximumFractionDigits: 2 });
    if (pnl > 0) {
        futuresPnlValueDisplay = `+${formattedPnl}`;
    } else {
        futuresPnlValueDisplay = formattedPnl;
    }
  } else {
     futuresPnlValueDisplay = (0).toLocaleString('en-US', { style: 'currency', currency: 'USD', minimumFractionDigits: 2, maximumFractionDigits: 2 });
  }


  let futuresPnlPercentageDisplay = "";
  if (!isFuturesPnlCardLoading && totalUnrealizedFuturesPnl !== null && accountOverview?.totalFuturesValueUSDT) {
      const futuresBalance = accountOverview.totalFuturesValueUSDT;
      if (futuresBalance > 0 && totalUnrealizedFuturesPnl !== 0) { 
        const perc = (totalUnrealizedFuturesPnl / futuresBalance) * 100;
        futuresPnlPercentageDisplay = `${perc.toFixed(1)}% sobre saldo`;
      } else if (totalUnrealizedFuturesPnl !== 0) { 
        futuresPnlPercentageDisplay = "N/A"; 
      } else { 
        futuresPnlPercentageDisplay = "0.0% sobre saldo";
      }
  } else if (isFuturesPnlCardLoading) {
      futuresPnlPercentageDisplay = ""; 
  } else {
      futuresPnlPercentageDisplay = "N/A"; 
  }


  const isOpenPositionsCounterLoading = isLoadingOpenPositions && openPositions.length === 0;
  const openPositionsCount = isOpenPositionsCounterLoading ? "..." : openPositionsError ? "Erro" : openPositions?.length ?? 0;

  const isTransactionsCounterLoading = isLoadingTransactions && recentTransactions.length === 0;
  const recentTransactionsCount7d = isTransactionsCounterLoading ? "..." : transactionsError ? "Erro" : recentTransactions?.filter(tx => new Date(tx.date) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)).length ?? 0;

  const isAlertsCounterLoading = isLoadingCryptos && alerts.length === 0; 
  const activeAlertsDisplay = isAlertsCounterLoading ? "..." : String(activeAlertsCount);


  return (
    <div className="flex flex-col gap-6" key="dashboard-content-wrapper">
      <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <Button onClick={handleManualRefresh} variant="outline" size="sm" disabled={isAnyDataLoading}>
              <RefreshCw className={`mr-2 h-4 w-4 ${isAnyDataLoading ? 'animate-spin' : ''}`} />
              Atualizar Dados
          </Button>
      </div>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
        <PortfolioSummaryCard
          title="Valor Total (Spot + Futuros)"
          value={totalValueDisplay}
          valueClassName="tabular-nums"
          change={totalValueChangeText}
          icon={DollarSign}
          iconColor="text-primary"
          isLoading={isLoadingAccountOverview && !accountOverview} 
          className="xl:col-span-1"
        />
        <PortfolioSummaryCard
          title="Saldo Carteira Spot"
          value={totalSpotValueDisplay}
          valueClassName="tabular-nums"
          icon={Wallet}
          iconColor="text-primary"
          isLoading={isLoadingAccountOverview && !accountOverview}
          className="xl:col-span-1"
        />
         <PortfolioSummaryCard
          title="Saldo Carteira Futuros"
          value={totalFuturesWalletDisplay}
          valueClassName="tabular-nums"
          icon={Briefcase}
          iconColor="text-primary"
          isLoading={isLoadingAccountOverview && !accountOverview}
          className="xl:col-span-1"
        />
        <PortfolioSummaryCard
          title="P&L Spot 24h"
          value={spotPnlValueDisplay}
          valueClassName="tabular-nums"
          change={spotPnlChangeText}
          icon={TrendingUp}
          iconColor={(accountOverview?.spotPnl24hUSDT ?? 0) >= 0 ? "text-green-500" : "text-red-500"}
          isLoading={isLoadingAccountOverview && !accountOverview}
          className="xl:col-span-1"
        />
        <PortfolioSummaryCard
          title="P&L Futuros (Abertas)"
          value={futuresPnlValueDisplay}
          valueClassName="tabular-nums"
          change={futuresPnlPercentageDisplay}
          icon={TrendingUp}
          iconColor={(totalUnrealizedFuturesPnl ?? 0) >= 0 ? "text-green-500" : "text-red-500"}
          isLoading={isLoadingOpenPositions && totalUnrealizedFuturesPnl === null}
          className="xl:col-span-1"
        />
      </div>

      {(!isKeysLoading && (accountOverviewError || openPositionsError || transactionsError) ) && (
        <Card className="bg-destructive/10 border-destructive text-destructive-foreground card-interactive">
            <CardHeader>
                <CardTitle className="flex items-center gap-2"><AlertTriangle/> Erro ao Carregar Dados</CardTitle>
            </CardHeader>
            <CardContent className="space-y-1 text-sm">
                {accountOverviewError && <p>Visão Geral/Ativos/Saldos: {accountOverviewError}</p>}
                {openPositionsError && <p>Posições Abertas / P&L Futuros: {openPositionsError}</p>}
                {transactionsError && <p>Transações Recentes: {transactionsError}</p>}
                <p className="mt-2">Por favor, verifique suas chaves de API nas <Link href="/settings" className="underline hover:text-destructive-foreground/80">configurações</Link> e a conexão com a Binance. Tente atualizar manualmente. Algumas funcionalidades podem estar limitadas.</p>
            </CardContent>
        </Card>
      )}

      <Card className="card-interactive">
        <CardHeader>
          <CardTitle>Meus Ativos (Spot + Saldo Futuros)</CardTitle>
          <CardDescription>Visão geral de suas criptomoedas e saldos em todas as carteiras conectadas.</CardDescription>
        </CardHeader>
        <CardContent className="min-h-[200px]">
          {isLoadingAccountOverview && !accountOverview?.assets?.length && !accountOverviewError ? (
            <div className="flex justify-center items-center py-10"><Loader2 className="h-8 w-8 animate-spin text-primary"/></div>
          ) : accountOverviewError ? (
            <p className="text-destructive text-center py-10">{accountOverviewError}</p>
          ) : (
            <AssetTable assets={accountOverview?.assets || []} />
          )}
        </CardContent>
      </Card>

      <div className="grid gap-4 md:grid-cols-3">
        <PortfolioSummaryCard
          title="Posições Abertas (Futuros)"
          value={String(openPositionsCount)}
          valueClassName="tabular-nums"
          icon={ListCollapse}
          iconColor="text-primary"
          isLoading={isLoadingOpenPositions && openPositions.length === 0} 
        />
         <PortfolioSummaryCard
          title="Transações Recentes (7d)"
          value={String(recentTransactionsCount7d)}
          valueClassName="tabular-nums"
          icon={Activity}
          iconColor="text-primary"
          isLoading={isLoadingTransactions && recentTransactions.length === 0} 
        />
        <PortfolioSummaryCard
            title="Alertas de Preço Ativos"
            value={activeAlertsDisplay}
            valueClassName="tabular-nums"
            icon={BellRing}
            iconColor="text-primary"
            isLoading={isLoadingCryptos && alerts.length === 0} 
        />
      </div>

      <FuturesTradingTerminal
        availableCryptos={cryptocurrencies.filter(c => c.symbol)}
        activeAlerts={alerts}
        currentPrices={currentPrices}
        onScrollToAlerts={handleScrollToAlerts}
        apiKey={apiKey}
        apiSecret={apiSecret}
      />

      <div className="grid gap-6 md:grid-cols-2">
        <Card className="card-interactive">
          <CardHeader>
            <CardTitle>Posições Abertas (Futuros)</CardTitle>
            <CardDescription>Suas posições de negociação ativas em futuros.</CardDescription>
          </CardHeader>
          <CardContent className="min-h-[200px]">
             {isLoadingOpenPositions && openPositions.length === 0 && !openPositionsError ? (
                <div className="flex justify-center items-center py-10"><Loader2 className="h-8 w-8 animate-spin text-primary"/></div>
              ) : openPositionsError ? (
                <p className="text-destructive text-center py-10">{openPositionsError}</p>
              ) : (
                <OpenPositionsTable 
                positions={openPositions} 
                apiKey={apiKey}
                apiSecret={apiSecret}
                onPositionUpdate={() => {
                  // Refresh positions data when a position is modified/closed
                  fetchOpenPositions();
                }}
              />
              )}
          </CardContent>
        </Card>
        <Card className="card-interactive">
          <CardHeader>
            <CardTitle>Histórico de Transações</CardTitle>
            <CardDescription>Sua atividade recente de transações.</CardDescription>
          </CardHeader>
          <CardContent className="min-h-[200px]">
            {isLoadingTransactions && recentTransactions.length === 0 && !transactionsError ? (
                <div className="flex justify-center items-center py-10"><Loader2 className="h-8 w-8 animate-spin text-primary"/></div>
              ) : transactionsError ? (
                <p className="text-destructive text-center py-10">{transactionsError}</p>
              ) : (
                <TransactionHistoryTable transactions={recentTransactions} />
              )}
          </CardContent>
        </Card>
      </div>

      <div ref={alertsSectionRef} id="alerts-section-on-dashboard">
        <Card className="card-interactive col-span-1 md:col-span-2 lg:col-span-3">
          <CardHeader>
              <CardTitle>Gerenciamento de Alertas de Preço</CardTitle>
              <CardDescription>Crie e gerencie seus alertas de preço diretamente no dashboard.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
              <div className={cn(
                  "text-sm text-muted-foreground flex items-center gap-2 p-3 border rounded-md shadow-sm",
                  (apiKey && apiSecret && activeAlertsCount > 0 && Object.keys(currentPrices).length > 0) ? "bg-green-500/10 border-green-500/30 text-green-700 dark:text-green-400" :
                  (apiKey && apiSecret && activeAlertsCount > 0 && Object.keys(currentPrices).length === 0 && !isLoadingAccountOverview) ? "bg-amber-500/10 border-amber-500/30 text-amber-700 dark:text-amber-500" : 
                  (apiKey && apiSecret && Object.keys(currentPrices).length === 0 && isLoadingAccountOverview) ? "bg-muted/50" : 
                  "bg-card/80"
                )}>
                  {apiKey && apiSecret && activeAlertsCount > 0 && Object.keys(currentPrices).length > 0 ? <Loader2 className="h-4 w-4 animate-spin" /> : 
                   (apiKey && apiSecret && Object.keys(currentPrices).length === 0 && isLoadingAccountOverview) ? <Loader2 className="h-4 w-4 animate-spin" /> :
                   <Info className="h-4 w-4" />
                  }
                  <span>
                  {apiKey && apiSecret ?
                      (activeAlertsCount > 0 && Object.keys(currentPrices).length > 0
                      ? `${activeAlertsCount} alerta(s) ativo(s) sendo monitorado(s). Preços atualizados a cada ${DASHBOARD_REFRESH_INTERVAL/1000}s.`
                      : (activeAlertsCount > 0 && Object.keys(currentPrices).length === 0 && !isLoadingAccountOverview ? "Monitorando alertas, aguardando dados de preço..." 
                      : (activeAlertsCount > 0 && Object.keys(currentPrices).length === 0 && isLoadingAccountOverview ? "Carregando preços para monitorar alertas..."
                      : "Nenhum alerta ativo para monitorar.")))
                      : "Configure as chaves de API nas Configurações para ativar o monitoramento de alertas."
                  }
                  </span>
              </div>

              {isLoadingCryptos && apiKey && apiSecret && (
                  <div className="flex flex-col items-center justify-center p-6 min-h-[100px] bg-muted/30 rounded-md">
                      <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
                      <p className="text-muted-foreground">Carregando criptomoedas disponíveis...</p>
                  </div>
              )}

              {errorCryptos && (
                  <div className="flex flex-col items-center justify-center p-4 min-h-[80px] bg-destructive/10 border border-destructive rounded-md">
                      <AlertTriangle className="h-7 w-7 text-destructive mb-1.5" />
                      <p className="text-destructive font-medium text-sm">Erro ao Carregar Criptomoedas</p>
                      <p className="text-destructive/80 text-xs text-center">{errorCryptos}</p>
                  </div>
              )}

              {(apiKey && apiSecret && !isLoadingCryptos && !errorCryptos && cryptocurrencies.length > 0) && (
                  <CreateAlertForm
                      onAlertCreated={handleAlertCreated}
                      availableCryptos={cryptocurrencies.filter(c => c.symbol)}
                  />
              )}
              {(apiKey && apiSecret && !isLoadingCryptos && !errorCryptos && cryptocurrencies.length === 0) && (
                  <p className="text-muted-foreground text-center py-4">Nenhuma criptomoeda disponível para criação de alertas no momento.</p>
              )}
              {(!apiKey || !apiSecret) && !isKeysLoading && (
                  <p className="text-muted-foreground text-center py-4">
                      Criação de alertas requer configuração das chaves de API nas <Link href="/settings" className="text-primary hover:underline">Configurações</Link>.
                  </p>
              )}

              <Separator />

              <ActiveAlertsList
                  alerts={alerts}
                  onDeleteAlert={handleDeleteAlert}
                  onToggleAlertStatus={handleToggleAlertStatus}
              />

              <Card className="mt-4 bg-muted/30 border-dashed">
                  <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-base"><Info className="text-blue-500 h-5 w-5"/>Como os Alertas Funcionam (Dashboard)</CardTitle>
                  </CardHeader>
                  <CardContent className="text-sm text-muted-foreground space-y-1.5">
                      <p><strong>Monitoramento:</strong> Alertas ativos são verificados contra os preços atuais do mercado (obtidos da Visão Geral da Conta, atualizada a cada ~{DASHBOARD_REFRESH_INTERVAL/1000} segundos) enquanto esta página do dashboard está aberta e as chaves de API estão configuradas.</p>
                      <p><strong>Ativo de Cotação:</strong> As verificações de preço assumem que o ativo de cotação é USDT (ex: BTC/USDT).</p>
                      <p><strong>Notificações:</strong> Alertas acionados exibirão uma notificação no aplicativo (toast). Notificações por email/push não fazem parte deste protótipo.</p>
                      <p><strong>Persistência:</strong> Alertas são salvos no `localStorage` do seu navegador e persistirão apenas para este navegador.</p>
                  </CardContent>
              </Card>
          </CardContent>
        </Card>
      </div>

       <Card className="mt-4 card-interactive">
          <CardHeader>
            <CardTitle className="flex items-center gap-2"><Info className="text-blue-500"/>Observações do Protótipo</CardTitle>
          </CardHeader>
          <CardContent className="text-sm text-muted-foreground space-y-2">
            <p><strong>Chaves de API:</strong> As chaves são armazenadas em `localStorage` e enviadas para as API routes do Next.js. Esta abordagem NÃO é segura para produção. Em um ambiente real, use um backend seguro para gerenciar chaves.</p>
            <p><strong>Cálculo de P&amp;L:</strong>
                O P&amp;L de 24h Spot é calculado pela variação de preço dos ativos spot.
                O P&amp;L de Futuros (Abertas) reflete o lucro/perda não realizado das posições de futuros atualmente abertas e sua porcentagem é calculada em relação ao saldo total da carteira de futuros.
            </p>
            <p><strong>Nomes e Ícones de Ativos:</strong> Nomes completos e ícones de criptomoedas são placeholders ou buscados de fontes públicas (nem todos os ativos terão ícones).</p>
            <p><strong>Histórico de Transações:</strong> A busca de histórico é limitada a alguns pares comuns e um número fixo de trades recentes para fins de demonstração.</p>
            <p><strong>Atualização de Dados:</strong> Os dados do dashboard são carregados quando a página é aberta e atualizados em intervalos regulares (ex: ~{DASHBOARD_REFRESH_INTERVAL/1000} segundos) se as chaves de API estiverem configuradas. A atualização frequente pode levar a bloqueios de IP pela Binance devido aos limites de taxa da API. Se encontrar erros de "Failed to fetch" ou dados desatualizados, pode ser necessário aumentar o intervalo de atualização, verificar suas chaves/permissões ou aguardar o desbloqueio do IP.</p>
            <p><strong>Terminal de Negociação:</strong> O terminal de negociação de futuros tenta executar **ORDENS REAIS** na Binance se as chaves de API do usuário (configuradas na página de Configurações) forem válidas e tiverem as permissões necessárias. USE COM EXTREMA CAUTELA E APENAS NO TESTNET OU COM VALORES MUITO BAIXOS APÓS ENTENDER OS RISCOS.</p>
          </CardContent>
        </Card>
    </div>
  );
}
