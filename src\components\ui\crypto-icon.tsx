"use client";

import { useState } from 'react';
import Image from 'next/image';
import { Coins } from 'lucide-react';

interface CryptoIconProps {
  symbol: string;
  size?: number;
  className?: string;
}

export function CryptoIcon({ symbol, size = 24, className = '' }: CryptoIconProps) {
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // List of known problematic symbols that should use fallback immediately
  const problematicSymbols = ['LDBTC', 'LDUSDC', 'LDSHIB2', 'ETHW', 'LDPEPE', 'MONKY', 'AKRO'];
  const shouldUseFallback = problematicSymbols.includes(symbol.toUpperCase());

  const iconUrl = `https://cdn.jsdelivr.net/gh/atomiclabs/cryptocurrency-icons/128/color/${symbol.toLowerCase()}.png`;

  if (hasError || shouldUseFallback) {
    return (
      <div 
        className={`flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-full ${className}`}
        style={{ width: size, height: size }}
      >
        <div className="text-xs font-semibold text-gray-600 dark:text-gray-300">
          {symbol.substring(0, Math.min(3, symbol.length)).toUpperCase()}
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`} style={{ width: size, height: size }}>
      {isLoading && (
        <div 
          className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-full animate-pulse"
        >
          <Coins size={size * 0.6} className="text-gray-400" />
        </div>
      )}
      <Image
        src={iconUrl}
        alt={`${symbol} icon`}
        width={size}
        height={size}
        className={`rounded-full ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-200`}
        onError={() => {
          setHasError(true);
          setIsLoading(false);
        }}
        onLoad={() => setIsLoading(false)}
        loading="lazy"
        unoptimized // This helps prevent Next.js optimization errors for external images
      />
    </div>
  );
} 