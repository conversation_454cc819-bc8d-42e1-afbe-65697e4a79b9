
"use client";

import Image from "next/image";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { CryptoIcon } from "@/components/ui/crypto-icon"; 
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import type { PortfolioAsset } from "@/lib/types";
import { ArrowUpRight, ArrowDownRight, MoreHorizontal, LineChart, Edit, BellPlus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";

interface AssetTableProps {
  assets: PortfolioAsset[];
}

const formatPriceForAssetTable = (price: number) => {
  if (price === 0) return "$0.00";
  let options: Intl.NumberFormatOptions = {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
  };
  if (Math.abs(price) < 0.01 && Math.abs(price) !== 0) {
    options.maximumFractionDigits = 8;
  } else if (Math.abs(price) < 1 && Math.abs(price) !== 0) {
    options.maximumFractionDigits = 6;
  } else {
    options.maximumFractionDigits = 2;
  }
  return price.toLocaleString(undefined, options);
};

const formatQuantityForAssetTable = (quantity: number) => {
  return quantity.toLocaleString(undefined, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 8,
  });
};

const formatValueForAssetTable = (value: number) => {
    return value.toLocaleString(undefined, {
        style: "currency",
        currency: "USD",
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    });
};

export function AssetTable({ assets }: AssetTableProps) {
  const router = useRouter();

  if (!assets || assets.length === 0) {
    return <p className="text-muted-foreground text-center py-4">Sem dados de ativos obtidos.</p>;
  }

  const handleViewDetails = (symbol: string) => {
    const quoteCurrencies = ['USDT', 'BUSD', 'BTC', 'ETH'];
    let pair = `${symbol.toUpperCase()}USDT`; 

    if (symbol.toUpperCase() === 'USDT' || symbol.toUpperCase() === 'BUSD') {
        window.open(`https://www.binance.com/en/price/${symbol.toLowerCase()}`, "_blank", "noopener noreferrer");
        return;
    }
    
    window.open(`https://www.binance.com/en/trade/${pair}?type=spot`, "_blank", "noopener noreferrer");
  };

  const handleCreateAlert = (symbol: string) => {
    const alertsSection = document.getElementById('alerts-section-on-dashboard'); 
    if (alertsSection) {
        alertsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    } else {
        console.warn("Alerts section (id='alerts-section-on-dashboard') not found for scrolling.");
    }
  };


  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead className="w-[50px] md:w-[80px]">Ícone</TableHead>
          <TableHead>Nome</TableHead>
          <TableHead className="hidden md:table-cell">Exchange</TableHead>
          <TableHead className="text-right">Quantidade</TableHead>
          <TableHead className="text-right">Preço (USD)</TableHead>
          <TableHead className="text-right">Valor (USD)</TableHead>
          <TableHead className="text-right hidden lg:table-cell">Variação 24h</TableHead>
          <TableHead className="text-right">Ações</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {assets.map((asset) => {
          return (
            <TableRow key={asset.id} className="hover:bg-muted/50">
              <TableCell>
                <CryptoIcon 
                  symbol={asset.symbol} 
                  size={32} 
                  className="bg-muted object-contain"
                />
              </TableCell>
              <TableCell>
                <div className="font-medium">{asset.name}</div>
                <div className="text-xs text-muted-foreground">{asset.symbol}</div>
              </TableCell>
              <TableCell className="hidden md:table-cell">
                <Badge variant="outline">{asset.exchange}</Badge>
              </TableCell>
              <TableCell className={cn("text-right", "tabular-nums")}>{formatQuantityForAssetTable(asset.quantity)}</TableCell>
              <TableCell className={cn("text-right", "tabular-nums")}>{formatPriceForAssetTable(asset.price)}</TableCell>
              <TableCell className={cn("text-right", "tabular-nums")}>{formatValueForAssetTable(asset.value)}</TableCell>
              <TableCell className="text-right hidden lg:table-cell">
                <Badge variant={asset.change24h >= 0 ? "default" : "destructive"} className={cn("tabular-nums", asset.change24h >=0 ? "bg-green-600/20 text-green-400 border-green-600/30 hover:bg-green-600/30" : "bg-red-600/20 text-red-400 border-red-600/30 hover:bg-red-600/30")}>
                  {asset.change24h >= 0 ? <ArrowUpRight className="h-3 w-3 mr-1" /> : <ArrowDownRight className="h-3 w-3 mr-1" />}
                  {asset.change24h.toFixed(2)}%
                </Badge>
              </TableCell>
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleViewDetails(asset.symbol)}>
                      <LineChart className="mr-2 h-4 w-4" />
                      Ver Detalhes/Gráfico
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleViewDetails(asset.symbol)}> 
                      <Edit className="mr-2 h-4 w-4" />
                      Negociar
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => handleCreateAlert(asset.symbol)}>
                      <BellPlus className="mr-2 h-4 w-4" />
                      Criar Alerta
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          );
        })}
      </TableBody>
    </Table>
  );
}
