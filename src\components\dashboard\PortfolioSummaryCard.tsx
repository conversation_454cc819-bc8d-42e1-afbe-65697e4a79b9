
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import type { LucideIcon } from "lucide-react";
import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

interface PortfolioSummaryCardProps {
  title: string;
  value: string;
  change?: string;
  icon: LucideIcon;
  iconColor?: string;
  isLoading?: boolean;
  className?: string;
  valueClassName?: string;
}

export function PortfolioSummaryCard({ title, value, change, icon: Icon, iconColor, isLoading, className, valueClassName }: PortfolioSummaryCardProps) {
  let changeTextColorClass = 'text-muted-foreground';

  if (change === "API Conectada") {
    changeTextColorClass = 'text-primary';
  } else if (iconColor && (change?.includes('%') || change?.includes('$'))) { // Apply iconColor if change likely represents P&L
    changeTextColorClass = iconColor;
  }


  return (
    <Card className={cn("card-interactive", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        {/* isLoading now only controls the loader icon, not the text content below */}
        {isLoading ? <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" /> : <Icon className={`h-6 w-6 ${iconColor || 'text-primary'}`} />}
      </CardHeader>
      <CardContent>
        <div className={cn("text-3xl font-bold", valueClassName)}>
          {/* Value is always rendered as passed from parent. Parent handles "..." or "Erro" logic. */}
          {value}
        </div>
        {/* Change is always rendered if available. Parent handles its content. */}
        {change && (
          <p className={`text-xs ${changeTextColorClass} dark:text-opacity-80`}>
            {change}
          </p>
        )}
      </CardContent>
    </Card>
  );
}
