@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 9%; /* Deep dark cool blue/grey */
    --foreground: 210 40% 95%; /* Bright near-white */

    --card: 210 40% 12%; /* Slightly lighter than background */
    --card-foreground: 210 40% 95%;

    --popover: 210 40% 12%;
    --popover-foreground: 210 40% 95%;

    --primary: 200 100% 50%; /* Vibrant cyan/blue */
    --primary-foreground: 210 40% 9%; /* Dark text for contrast on primary */

    --secondary: 210 40% 18%; /* Darker secondary for subtle elements */
    --secondary-foreground: 210 40% 85%;

    --muted: 210 40% 15%;
    --muted-foreground: 210 40% 65%;

    --accent: 270 100% 60%; /* Vibrant purple/magenta */
    --accent-foreground: 210 40% 95%; /* Light text for contrast on accent */

    --destructive: 0 72% 51%; /* Kept similar for clear error indication */
    --destructive-foreground: 0 0% 98%;

    --border: 210 40% 20%; /* Subtle but visible borders */
    --input: 210 40% 16%; /* Input background */
    --ring: 200 100% 50%; /* Ring matches primary */

    --radius: 0.5rem;

    --chart-1: hsl(var(--primary));
    --chart-2: hsl(var(--accent));
    --chart-3: 170 80% 45%; /* Contrasting teal/green */
    --chart-4: 30 100% 55%; /* Warm orange */
    --chart-5: 330 90% 60%; /* Pink/rose */

    /* Sidebar specific theme variables */
    --sidebar-background: 210 40% 7%; /* Even darker for depth */
    --sidebar-foreground: 210 40% 80%;
    --sidebar-primary: hsl(var(--primary));
    --sidebar-primary-foreground: hsl(var(--primary-foreground));
    --sidebar-accent: 210 40% 15%;
    --sidebar-accent-foreground: 210 40% 95%;
    --sidebar-border: 210 40% 15%;
    --sidebar-ring: hsl(var(--primary));
  }

  .dark {
    --background: 210 40% 9%;
    --foreground: 210 40% 95%;
    --card: 210 40% 12%;
    --card-foreground: 210 40% 95%;
    --popover: 210 40% 12%;
    --popover-foreground: 210 40% 95%;
    --primary: 200 100% 50%;
    --primary-foreground: 210 40% 9%;
    --secondary: 210 40% 18%;
    --secondary-foreground: 210 40% 85%;
    --muted: 210 40% 15%;
    --muted-foreground: 210 40% 65%;
    --accent: 270 100% 60%;
    --accent-foreground: 210 40% 95%;
    --destructive: 0 72% 51%;
    --destructive-foreground: 0 0% 98%;
    --border: 210 40% 20%;
    --input: 210 40% 16%;
    --ring: 200 100% 50%;

    --chart-1: hsl(var(--primary));
    --chart-2: hsl(var(--accent));
    --chart-3: 170 80% 45%;
    --chart-4: 30 100% 55%;
    --chart-5: 330 90% 60%;
    
    --sidebar-background: 210 40% 7%;
    --sidebar-foreground: 210 40% 80%;
    --sidebar-primary: hsl(var(--primary));
    --sidebar-primary-foreground: hsl(var(--primary-foreground));
    --sidebar-accent: 210 40% 15%;
    --sidebar-accent-foreground: 210 40% 95%;
    --sidebar-border: 210 40% 15%;
    --sidebar-ring: hsl(var(--primary));
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-geist-sans), sans-serif;
  }
}

@layer components {
  .card-interactive {
    @apply rounded-lg border bg-card text-card-foreground shadow-lg transition-all duration-300 ease-in-out hover:shadow-2xl hover:scale-[1.015] hover:border-primary/60;
  }
}
