// src/ai/flows/summarize-market-news.ts
'use server';

/**
 * @fileOverview Summarizes market news relevant to a user's cryptocurrency portfolio.
 *
 * - summarizeMarketNews - A function that summarizes market news for a user's portfolio.
 * - SummarizeMarketNewsInput - The input type for the summarizeMarketNews function.
 * - SummarizeMarketNewsOutput - The return type for the summarizeMarketNews function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const SummarizeMarketNewsInputSchema = z.object({
  portfolio: z
    .array(z.string())
    .describe('An array of cryptocurrency symbols in the user\'s portfolio.'),
  newsArticles: z.array(z.string()).describe('An array of market news articles.'),
});
export type SummarizeMarketNewsInput = z.infer<typeof SummarizeMarketNewsInputSchema>;

const SummarizeMarketNewsOutputSchema = z.object({
  summary: z.string().describe('A summary of the market news relevant to the user\'s portfolio.'),
});
export type SummarizeMarketNewsOutput = z.infer<typeof SummarizeMarketNewsOutputSchema>;

export async function summarizeMarketNews(input: SummarizeMarketNewsInput): Promise<SummarizeMarketNewsOutput> {
  return summarizeMarketNewsFlow(input);
}

const prompt = ai.definePrompt({
  name: 'summarizeMarketNewsPrompt',
  input: {schema: SummarizeMarketNewsInputSchema},
  output: {schema: SummarizeMarketNewsOutputSchema},
  prompt: `You are an AI assistant that summarizes market news for cryptocurrency investors.

You will receive a list of cryptocurrency symbols in the user's portfolio and a list of market news articles.
Your task is to summarize the market news articles, focusing on the information that is most relevant to the user's portfolio.
Consider any mentioned coins from the user portfolio and how the provided articles might impact them.

Portfolio: {{{portfolio}}}
News Articles: {{{newsArticles}}}

Summary:`,
});

const summarizeMarketNewsFlow = ai.defineFlow(
  {
    name: 'summarizeMarketNewsFlow',
    inputSchema: SummarizeMarketNewsInputSchema,
    outputSchema: SummarizeMarketNewsOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
